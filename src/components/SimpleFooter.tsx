import React from 'react';
import { Phone, Mail, MapPin } from 'lucide-react';

const SimpleFooter: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-xl font-bold mb-4">Horizon Carpentry</h3>
            <p className="text-gray-300 mb-4">
              Professional construction and home improvement services throughout Georgia. 
              Quality craftsmanship you can trust.
            </p>
            <div className="flex items-center text-gray-300 mb-2">
              <Phone className="w-4 h-4 mr-2" />
              <span>(912) XXX-XXXX</span>
            </div>
            <div className="flex items-center text-gray-300 mb-2">
              <Mail className="w-4 h-4 mr-2" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center text-gray-300">
              <MapPin className="w-4 h-4 mr-2" />
              <span>Serving Southeast Georgia</span>
            </div>
          </div>
          
          {/* Services */}
          <div>
            <h3 className="text-xl font-bold mb-4">Our Services</h3>
            <ul className="space-y-2 text-gray-300">
              <li><a href="#" className="hover:text-white transition-colors">Deck Construction</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Bathroom Remodeling</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Kitchen Remodeling</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Home Repairs</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Handyman Services</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Custom Carpentry</a></li>
            </ul>
          </div>
          
          {/* Service Areas */}
          <div>
            <h3 className="text-xl font-bold mb-4">Service Areas</h3>
            <ul className="space-y-2 text-gray-300">
              <li><a href="/brunswick" className="hover:text-white transition-colors">Brunswick</a></li>
              <li><a href="/jekyll-island" className="hover:text-white transition-colors">Jekyll Island</a></li>
              <li><a href="/st-simons-island" className="hover:text-white transition-colors">St. Simons Island</a></li>
              <li><a href="/glynn-county" className="hover:text-white transition-colors">Glynn County</a></li>
              <li><a href="/waycross" className="hover:text-white transition-colors">Waycross</a></li>
              <li><a href="/kingsland" className="hover:text-white transition-colors">Kingsland</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
          <p>&copy; 2024 Horizon Carpentry. All rights reserved. Licensed & Insured.</p>
        </div>
      </div>
    </footer>
  );
};

export default SimpleFooter;
