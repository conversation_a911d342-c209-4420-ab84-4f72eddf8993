import React, { useState } from 'react';

// HighLevel Configuration
const DEFAULT_WEBHOOK_URL = import.meta.env.VITE_HIGHLEVEL_WEBHOOK_URL || 'https://services.leadconnectorhq.com/hooks/aQYV8jwYWM9za5egdIl2/webhook-trigger/MtXbX5CeppxgHBWnTW5S';
const DEFAULT_LOCATION_ID = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID || 'aQYV8jwYWM9za5egdIl2';

interface CompactHighLevelFormProps {
  title?: string;
  location?: string;
  service?: string;
}

const CompactHighLevelForm: React.FC<CompactHighLevelFormProps> = ({
  title = "Get Your Free Project Estimate",
  location,
  service
}) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    email: '',
    consent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormStatus('Submitting...');

    // Prepare the payload with HighLevel fields
    const payload = {
      ...formData,
      // Add HighLevel specific fields
      source: 'horizon_carpentry_hero',
      location_id: DEFAULT_LOCATION_ID,
      submitted_at: new Date().toISOString(),
      full_name: `${formData.first_name} ${formData.last_name}`.trim(),
      // Add location and service context if provided
      ...(location && { project_location: location }),
      ...(service && { service_interest: service }),
    };

    try {
      const response = await fetch(DEFAULT_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        setFormStatus('Thank you! We will contact you soon.');
        // Reset form
        setFormData({
          first_name: '',
          last_name: '',
          phone: '',
          email: '',
          consent: false
        });
      } else {
        setFormStatus('Error submitting. Please try again.');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setFormStatus('Error submitting. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  return (
    <div className="w-full max-w-sm bg-blue-600 text-white rounded-2xl shadow-2xl p-6">
      <h3 className="text-xl font-bold text-center mb-5">{title}</h3>
      
      <form onSubmit={handleSubmit} className="space-y-3">
        <input
          type="text"
          name="first_name"
          value={formData.first_name}
          onChange={handleChange}
          placeholder="First Name"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <input
          type="text"
          name="last_name"
          value={formData.last_name}
          onChange={handleChange}
          placeholder="Last Name"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <input
          type="tel"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          placeholder="Phone Number"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="Email Address"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <div className="flex items-start text-xs">
          <input
            type="checkbox"
            id="consent"
            name="consent"
            checked={formData.consent}
            onChange={handleChange}
            required
            className="mt-1 mr-2"
          />
          <label htmlFor="consent" className="text-blue-100">
            I agree to receive communications regarding my project inquiry. *
          </label>
        </div>
        
        {formStatus && (
          <div className={`text-center text-sm p-2 rounded ${
            formStatus.includes('Error') || formStatus.includes('error')
              ? 'bg-red-500 text-white'
              : formStatus.includes('Thank you')
              ? 'bg-green-500 text-white'
              : 'bg-blue-500 text-white'
          }`}>
            {formStatus}
          </div>
        )}
        
        <button
          type="submit"
          disabled={isSubmitting || !formData.consent}
          className={`w-full py-3 px-4 rounded-lg font-semibold text-sm transition-colors ${
            isSubmitting || !formData.consent
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-orange-500 hover:bg-orange-600'
          } text-white`}
        >
          {isSubmitting ? 'Submitting...' : 'Get Free Estimate'}
        </button>
      </form>
    </div>
  );
};

export default CompactHighLevelForm;
