import React, { useState, useRef } from 'react';

// Configuration
const CLOUDFLARE_WORKER_URL = import.meta.env.VITE_IMAGE_UPLOAD_URL || '';
const CLOUDFLARE_AUTH_KEY = import.meta.env.VITE_IMAGE_UPLOAD_TOKEN || '';
const DEFAULT_WEBHOOK_URL = import.meta.env.VITE_HIGHLEVEL_WEBHOOK_URL || 'https://services.leadconnectorhq.com/hooks/aQYV8jwYWM9za5egdIl2/webhook-trigger/MtXbX5CeppxgHBWnTW5S';
const DEFAULT_LOCATION_ID = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID || 'aQYV8jwYWM9za5egdIl2';

// Helper component for the upload icon
const UploadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-10 w-10 text-orange-200" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1">
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

// Helper component for the loading spinner
const Loader = () => (
  <div className="loader border-4 border-orange-200 border-t-4 border-white rounded-full w-6 h-6 animate-spin"></div>
);

interface CompactHighLevelFormProps {
  title?: string;
  location?: string;
  service?: string;
}

const CompactHighLevelForm: React.FC<CompactHighLevelFormProps> = ({
  title = "Get Your Free Project Estimate",
  location,
  service
}) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    email: '',
    project_image_url: '',
    consent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState('');
  const [uploadStatus, setUploadStatus] = useState('');
  const [imagePreview, setImagePreview] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handler for file selection (from click or drop)
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadImage(file);
    }
  };

  // Function to upload the image to Cloudflare Worker
  const uploadImage = async (file: File) => {
    setIsUploading(true);
    setUploadStatus('Uploading...');
    const uploadFormData = new FormData();
    uploadFormData.append('image', file);

    console.log('=== COMPACT FORM IMAGE UPLOAD DEBUG ===');
    console.log('Uploading to:', CLOUDFLARE_WORKER_URL);
    console.log('Auth key:', CLOUDFLARE_AUTH_KEY);
    console.log('File:', file.name, file.size, file.type);

    try {
      const response = await fetch(CLOUDFLARE_WORKER_URL, {
        method: 'POST',
        body: uploadFormData,
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_AUTH_KEY}`,
        },
      });

      console.log('Response status:', response.status);
      const responseText = await response.text();
      console.log('Response body (raw):', responseText);

      // Try to parse as JSON, but handle HTML error responses
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        console.error('Response was:', responseText.substring(0, 500) + '...');

        // Show user-friendly error with first part of response
        const preview = responseText.substring(0, 100).replace(/</g, '&lt;').replace(/>/g, '&gt;');
        throw new Error(`Worker returned HTML instead of JSON. Preview: ${preview}...`);
      }

      if (result.success) {
        setFormData(prev => ({ ...prev, project_image_url: result.url }));
        setImagePreview(result.url);
        setUploadStatus('Uploaded!');
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload failed:', error);
      setUploadStatus('Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      uploadImage(file);
    } else {
      setUploadStatus('Please upload an image file');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormStatus('Submitting...');

    // Prepare the payload with HighLevel fields
    const payload = {
      ...formData,
      // Add HighLevel specific fields
      source: 'horizon_carpentry_hero',
      location_id: DEFAULT_LOCATION_ID,
      submitted_at: new Date().toISOString(),
      full_name: `${formData.first_name} ${formData.last_name}`.trim(),
      // Add location and service context if provided
      ...(location && { project_location: location }),
      ...(service && { service_interest: service }),
    };

    try {
      const response = await fetch(DEFAULT_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        setFormStatus('Thank you! We will contact you soon.');
        // Reset form
        setFormData({
          first_name: '',
          last_name: '',
          phone: '',
          email: '',
          project_image_url: '',
          consent: false
        });
        setImagePreview('');
        setUploadStatus('');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        setFormStatus('Error submitting. Please try again.');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setFormStatus('Error submitting. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  return (
    <div className="w-full max-w-sm bg-blue-600 text-white rounded-2xl shadow-2xl p-6">
      <h3 className="text-xl font-bold text-center mb-5">{title}</h3>
      
      <form onSubmit={handleSubmit} className="space-y-3">
        <input
          type="text"
          name="first_name"
          value={formData.first_name}
          onChange={handleChange}
          placeholder="First Name"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <input
          type="text"
          name="last_name"
          value={formData.last_name}
          onChange={handleChange}
          placeholder="Last Name"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <input
          type="tel"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          placeholder="Phone Number"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />
        
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="Email Address"
          className="w-full p-2.5 rounded-lg text-gray-900 text-sm"
          required
        />

        {/* Image Upload Section */}
        <div
          className="bg-orange-500 border-2 border-dashed border-orange-300 rounded-lg p-4 text-center cursor-pointer hover:bg-orange-400 transition-colors"
          onClick={() => fileInputRef.current?.click()}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {imagePreview ? (
            <img src={imagePreview} alt="Project preview" className="mx-auto h-12 w-12 object-cover rounded-md" />
          ) : (
            <>
              <UploadIcon />
              <div className="font-medium text-white text-sm mt-1">
                Image Upload of Project
              </div>
              <p className="text-xs text-orange-200 mt-1">PNG, JPG, GIF up to 10MB</p>
            </>
          )}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>

        {uploadStatus && (
          <div className="text-center text-xs flex items-center justify-center gap-2">
            {isUploading && <Loader />}
            <span className={uploadStatus.includes('failed') ? 'text-red-300' : uploadStatus.includes('Uploaded') ? 'text-green-300' : 'text-orange-200'}>
              {uploadStatus}
            </span>
          </div>
        )}

        <div className="flex items-start text-xs">
          <input
            type="checkbox"
            id="consent"
            name="consent"
            checked={formData.consent}
            onChange={handleChange}
            required
            className="mt-1 mr-2"
          />
          <label htmlFor="consent" className="text-blue-100">
            I agree to receive communications regarding my project inquiry. *
          </label>
        </div>
        
        {formStatus && (
          <div className={`text-center text-sm p-2 rounded ${
            formStatus.includes('Error') || formStatus.includes('error')
              ? 'bg-red-500 text-white'
              : formStatus.includes('Thank you')
              ? 'bg-green-500 text-white'
              : 'bg-blue-500 text-white'
          }`}>
            {formStatus}
          </div>
        )}
        
        <button
          type="submit"
          disabled={isSubmitting || !formData.consent}
          className={`w-full py-3 px-4 rounded-lg font-semibold text-sm transition-colors ${
            isSubmitting || !formData.consent
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-orange-500 hover:bg-orange-600'
          } text-white`}
        >
          {isSubmitting ? 'Submitting...' : 'Get Free Estimate'}
        </button>
      </form>
    </div>
  );
};

export default CompactHighLevelForm;
