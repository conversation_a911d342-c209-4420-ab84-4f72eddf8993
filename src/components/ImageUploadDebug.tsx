import React, { useState } from 'react';

const ImageUploadDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<string>('');

  const testUpload = async () => {
    const CLOUDFLARE_WORKER_URL = import.meta.env.VITE_IMAGE_UPLOAD_URL || '';
    const CLOUDFLARE_AUTH_KEY = import.meta.env.VITE_IMAGE_UPLOAD_TOKEN || '';

    setDebugInfo('Starting debug test...\n');
    
    // Log environment variables
    setDebugInfo(prev => prev + `Worker URL: ${CLOUDFLARE_WORKER_URL}\n`);
    setDebugInfo(prev => prev + `Auth Key: ${CLOUDFLARE_AUTH_KEY}\n`);
    
    // Test with a simple fetch first (no file)
    try {
      setDebugInfo(prev => prev + '\n--- Testing Worker Endpoint ---\n');
      
      const testResponse = await fetch(CLOUDFLARE_WORKER_URL, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_AUTH_KEY}`,
        },
      });
      
      setDebugInfo(prev => prev + `GET Response Status: ${testResponse.status}\n`);
      setDebugInfo(prev => prev + `GET Response Headers: ${JSON.stringify(Object.fromEntries(testResponse.headers.entries()), null, 2)}\n`);
      
      const testResponseText = await testResponse.text();
      setDebugInfo(prev => prev + `GET Response Body: ${testResponseText}\n`);
      
    } catch (error) {
      setDebugInfo(prev => prev + `GET Error: ${error}\n`);
    }

    // Test with OPTIONS request (CORS preflight)
    try {
      setDebugInfo(prev => prev + '\n--- Testing CORS ---\n');
      
      const corsResponse = await fetch(CLOUDFLARE_WORKER_URL, {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Authorization, Content-Type',
        },
      });
      
      setDebugInfo(prev => prev + `OPTIONS Response Status: ${corsResponse.status}\n`);
      setDebugInfo(prev => prev + `OPTIONS Response Headers: ${JSON.stringify(Object.fromEntries(corsResponse.headers.entries()), null, 2)}\n`);
      
    } catch (error) {
      setDebugInfo(prev => prev + `OPTIONS Error: ${error}\n`);
    }
  };

  const testFileUpload = async () => {
    const CLOUDFLARE_WORKER_URL = import.meta.env.VITE_IMAGE_UPLOAD_URL || '';
    const CLOUDFLARE_AUTH_KEY = import.meta.env.VITE_IMAGE_UPLOAD_TOKEN || '';

    // Create a small test image (1x1 pixel PNG)
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = 'red';
      ctx.fillRect(0, 0, 1, 1);
    }

    canvas.toBlob(async (blob) => {
      if (!blob) {
        setDebugInfo(prev => prev + '\nFailed to create test image blob\n');
        return;
      }

      setDebugInfo(prev => prev + '\n--- Testing File Upload ---\n');
      setDebugInfo(prev => prev + `Test file size: ${blob.size} bytes\n`);
      setDebugInfo(prev => prev + `Test file type: ${blob.type}\n`);

      const formData = new FormData();
      formData.append('image', blob, 'test.png');

      try {
        const response = await fetch(CLOUDFLARE_WORKER_URL, {
          method: 'POST',
          body: formData,
          headers: {
            'Authorization': `Bearer ${CLOUDFLARE_AUTH_KEY}`,
          },
        });

        setDebugInfo(prev => prev + `POST Response Status: ${response.status}\n`);
        setDebugInfo(prev => prev + `POST Response Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}\n`);

        const responseText = await response.text();
        setDebugInfo(prev => prev + `POST Response Body: ${responseText}\n`);

        // Try to parse as JSON
        try {
          const jsonResult = JSON.parse(responseText);
          setDebugInfo(prev => prev + `Parsed JSON: ${JSON.stringify(jsonResult, null, 2)}\n`);
        } catch (parseError) {
          setDebugInfo(prev => prev + `JSON Parse Error: ${parseError}\n`);
          setDebugInfo(prev => prev + `Response appears to be HTML or other format\n`);
        }

      } catch (error) {
        setDebugInfo(prev => prev + `POST Error: ${error}\n`);
      }
    }, 'image/png');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto">
      <h3 className="text-xl font-bold mb-4">Image Upload Debug Tool</h3>
      
      <div className="space-x-4 mb-4">
        <button
          onClick={testUpload}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Worker Endpoint
        </button>
        
        <button
          onClick={testFileUpload}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Test File Upload
        </button>
        
        <button
          onClick={() => setDebugInfo('')}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear Log
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded">
        <h4 className="font-semibold mb-2">Debug Output:</h4>
        <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-96">
          {debugInfo || 'Click "Test Worker Endpoint" or "Test File Upload" to start debugging...'}
        </pre>
      </div>
    </div>
  );
};

export default ImageUploadDebug;
