import React from 'react';
import { Star, Phone } from 'lucide-react';
import CompactHighLevelForm from './CompactHighLevelForm';

interface SimpleHeroSectionProps {
  location?: string;
  title?: string;
  subtitle?: string;
  service?: string;
  showForm?: boolean;
}

const SimpleHeroSection: React.FC<SimpleHeroSectionProps> = ({
  location = "Georgia",
  title = "Professional Construction Services",
  subtitle = "Expert craftsmanship and reliable service",
  service,
  showForm = true
}) => {
  const displayTitle = service 
    ? `${service} in ${location}`
    : title + (location !== "Georgia" ? ` in ${location}` : "");

  return (
    <section className="relative bg-gradient-to-br from-blue-900 to-blue-700 text-white py-20 mt-16">
      <div className="absolute inset-0 bg-black opacity-20"></div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {displayTitle}
            </h1>
            <p className="text-xl text-blue-100 mb-8">
              {subtitle}
            </p>
            
            {/* Star Rating */}
            <div className="flex items-center mb-8">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="ml-2 text-blue-100">5.0/5 Stars - 200+ Reviews</span>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <a 
                href="#contact" 
                className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors text-center"
              >
                Get Free Estimate
              </a>
              <a 
                href="tel:******-XXX-XXXX" 
                className="border-2 border-white text-white hover:bg-white hover:text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors text-center flex items-center justify-center"
              >
                <Phone className="w-4 h-4 mr-2" />
                Call Now
              </a>
            </div>
          </div>
          
          <div className="lg:text-right">
            {showForm ? (
              <CompactHighLevelForm
                location={location}
                service={service}
                title="Get Your Free Project Estimate"
              />
            ) : (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4">Why Choose Us?</h3>
                <ul className="space-y-3 text-left">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                    Licensed & Insured
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                    Free Estimates
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                    Quality Guarantee
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                    Local Experts
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SimpleHeroSection;
