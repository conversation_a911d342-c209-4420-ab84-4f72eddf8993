import React, { useState, useRef } from 'react';
import { Phone, Mail, Calendar, Upload, X } from 'lucide-react';
import { uploadImage, getImageUploadConfig, type ImageUploadResponse } from '../utils/imageupload';

// Configuration
const DEFAULT_WEBHOOK_URL = import.meta.env.VITE_HIGHLEVEL_WEBHOOK_URL || 'https://services.leadconnectorhq.com/hooks/aQYV8jwYWM9za5egdIl2/webhook-trigger/MtXbX5CeppxgHBWnTW5S';
const DEFAULT_LOCATION_ID = import.meta.env.VITE_HIGHLEVEL_LOCATION_ID || 'aQYV8jwYWM9za5egdIl2';
const BACKUP_WEBHOOK_URL = import.meta.env.VITE_BACKUP_WEBHOOK_URL || '';

// Helper component for the upload icon
const UploadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-10 w-10 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1">
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

// Helper component for the loading spinner
const Loader = () => (
  <div className="loader border-4 border-gray-200 border-t-4 border-blue-600 rounded-full w-6 h-6 animate-spin"></div>
);

const EnhancedBookingSection: React.FC = () => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: 'Georgia',
    country: 'United States',
    postal_code: '',
    service: '',
    message: '',
    project_image_url: '',
    consent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState('');
  const [uploadStatus, setUploadStatus] = useState('');
  const [imagePreview, setImagePreview] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handler for file selection (from click or drop)
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  // Function to upload image to Cloudflare R2
  const handleImageUpload = async (file: File) => {
    setIsUploading(true);
    setUploadStatus('Uploading to R2...');

    try {
      const config = getImageUploadConfig();
      const result = await uploadImage(file, config);

      if (result.success && result.url) {
        // Store the R2 URL
        setFormData(prev => ({ ...prev, project_image_url: result.url }));
        setImagePreview(result.url);
        setUploadStatus('Uploaded to R2!');
        console.log('Image uploaded to R2:', result.url);
      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error) {
      console.error('R2 upload failed:', error);
      setUploadStatus('R2 upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.add('border-blue-400', 'bg-blue-50');
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.remove('border-blue-400', 'bg-blue-50');
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.remove('border-blue-400', 'bg-blue-50');
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleImageUpload(file);
    } else {
      setUploadStatus('Please upload an image file (PNG, JPG, GIF)');
    }
  };

  // Remove uploaded image
  const removeImage = () => {
    setImagePreview('');
    setFormData(prev => ({ ...prev, project_image_url: '' }));
    setUploadStatus('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormStatus('Submitting...');

    // Prepare the payload with HighLevel fields
    const payload = {
      ...formData,
      // Add HighLevel specific fields
      source: 'horizon_carpentry_enhanced',
      location_id: DEFAULT_LOCATION_ID,
      submitted_at: new Date().toISOString(),
      full_name: `${formData.first_name} ${formData.last_name}`.trim(),
    };

    console.log('=== FORM SUBMISSION DEBUG ===');
    console.log('Webhook URL:', DEFAULT_WEBHOOK_URL);
    console.log('Payload:', payload);

    try {
      const response = await fetch(DEFAULT_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      console.log('Response status:', response.status);
      const responseText = await response.text();
      console.log('Response body:', responseText);

      // Also send to backup webhook if configured
      if (BACKUP_WEBHOOK_URL) {
        try {
          console.log('Also sending to backup webhook...');
          await fetch(BACKUP_WEBHOOK_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ...payload,
              note: 'Backup webhook - HighLevel primary submission with image',
              webhook_source: 'horizon_carpentry_enhanced',
              primary_webhook_status: response.status,
              primary_webhook_response: responseText
            })
          });
        } catch (backupError) {
          console.error('Backup webhook failed:', backupError);
        }
      }

      if (response.ok) {
        setFormStatus('Thank you! We will contact you soon.');
        // Reset form
        setFormData({
          first_name: '',
          last_name: '',
          email: '',
          phone: '',
          address: '',
          city: '',
          state: 'Georgia',
          country: 'United States',
          postal_code: '',
          service: '',
          message: '',
          project_image_url: '',
          consent: false
        });
        setImagePreview('');
        setUploadStatus('');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        setFormStatus('There was an error submitting your request. Please try again.');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setFormStatus('There was an error submitting your request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    });
  };

  return (
    <section id="contact" className="py-16 bg-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Get Your Free Estimate Today
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Ready to start your project? Contact us for a free, no-obligation estimate. 
            Upload a photo of your project area to help us provide a more accurate quote.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Request a Quote</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-1">
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="first_name"
                    name="first_name"
                    required
                    value={formData.first_name}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="last_name"
                    name="last_name"
                    required
                    value={formData.last_name}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  required
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-1">
                  Service Needed
                </label>
                <select
                  id="service"
                  name="service"
                  value={formData.service}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a service</option>
                  <option value="deck-construction">Deck Construction</option>
                  <option value="bathroom-remodeling">Bathroom Remodeling</option>
                  <option value="kitchen-remodeling">Kitchen Remodeling</option>
                  <option value="home-repairs">Home Repairs</option>
                  <option value="handyman-services">Handyman Services</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Details
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Tell us about your project..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>

              {/* Image Upload Section */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Photos (Optional)
                </label>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-400 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Project preview"
                        className="mx-auto h-32 w-auto object-cover rounded-md"
                      />
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage();
                        }}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <>
                      <UploadIcon />
                      <div className="font-medium text-gray-700 mt-2">
                        Upload Project Photos
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        Drag and drop or click to select
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>

                {uploadStatus && (
                  <div className="mt-2 text-center text-sm flex items-center justify-center gap-2">
                    {isUploading && <Loader />}
                    <span className={uploadStatus.includes('failed') || uploadStatus.includes('Upload failed')
                      ? 'text-red-600'
                      : uploadStatus.includes('successfully')
                      ? 'text-green-600'
                      : 'text-blue-600'
                    }>
                      {uploadStatus}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="consent"
                  name="consent"
                  checked={formData.consent}
                  onChange={handleChange}
                  required
                  className="mt-1 mr-2"
                />
                <label htmlFor="consent" className="text-sm text-gray-600">
                  I agree to receive communications from Horizon Carpentry regarding my project inquiry. *
                </label>
              </div>

              {formStatus && (
                <div className={`text-center p-3 rounded-md ${
                  formStatus.includes('error') || formStatus.includes('Error')
                    ? 'bg-red-100 text-red-700'
                    : formStatus.includes('Thank you')
                    ? 'bg-green-100 text-green-700'
                    : 'bg-blue-100 text-blue-700'
                }`}>
                  {formStatus}
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting || !formData.consent}
                className={`w-full py-3 px-6 rounded-md font-semibold transition-colors ${
                  isSubmitting || !formData.consent
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white`}
              >
                {isSubmitting ? 'Submitting...' : 'Get Free Estimate'}
              </button>
            </form>
          </div>

          {/* Contact Info */}
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Contact Information</h3>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Phone className="w-6 h-6 text-blue-600 mr-4" />
                  <div>
                    <p className="font-medium text-gray-900">Call Us</p>
                    <p className="text-gray-600">(912) XXX-XXXX</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <Mail className="w-6 h-6 text-blue-600 mr-4" />
                  <div>
                    <p className="font-medium text-gray-900">Email Us</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center">
                  <Calendar className="w-6 h-6 text-blue-600 mr-4" />
                  <div>
                    <p className="font-medium text-gray-900">Business Hours</p>
                    <p className="text-gray-600">Mon-Fri: 7AM-6PM<br />Sat: 8AM-4PM</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-600 text-white rounded-lg p-8">
              <h3 className="text-xl font-semibold mb-4">Why Upload Photos?</h3>
              <ul className="space-y-2 text-blue-100">
                <li>• Get more accurate estimates</li>
                <li>• Faster project planning</li>
                <li>• Better material recommendations</li>
                <li>• Identify potential challenges early</li>
              </ul>
              <div className="mt-4 p-3 bg-blue-500 rounded">
                <p className="text-sm">
                  <strong>Tip:</strong> Include photos from multiple angles for the best estimate!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnhancedBookingSection;
