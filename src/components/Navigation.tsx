import React, { useState, useEffect } from 'react';
import { Menu, X, Phone, ChevronDown } from 'lucide-react';

interface NavigationProps {
  currentLocation?: string;
  currentService?: string;
}

const Navigation: React.FC<NavigationProps> = ({ currentLocation, currentService }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [locationsOpen, setLocationsOpen] = useState(false);
  const [servicesOpen, setServicesOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 100;
      setIsScrolled(scrolled);
    };

    handleScroll();
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Gallery', href: '#gallery' },
    { name: 'Reviews', href: '#reviews' },
    { name: 'About', href: '#about' },
    { name: 'Contact', href: '#contact' },
  ];

  const locations = [
    { name: 'Brunswick', slug: 'brunswick' },
    { name: 'Jekyll Island', slug: 'jekyll-island' },
    { name: 'Glynn County', slug: 'glynn-county' },
    { name: 'St. Simons Island', slug: 'st-simons-island' },
    { name: 'Waycross', slug: 'waycross' },
    { name: 'Woodbine', slug: 'woodbine' },
    { name: 'Sea Island', slug: 'sea-island' },
    { name: 'Camden County', slug: 'camden-county' },
    { name: 'Brantley County', slug: 'brantley-county' },
    { name: 'Kingsland', slug: 'kingsland' },
    { name: 'Darien', slug: 'darien' },
    { name: 'McIntosh County', slug: 'mcintosh-county' }
  ];

  const services = [
    { name: 'Deck Construction', slug: 'deck-construction' },
    { name: 'Bathroom Remodeling', slug: 'bathroom-remodeling' },
    { name: 'Home Repairs', slug: 'home-repairs' },
    { name: 'Handyman Services', slug: 'handyman-services' },
    { name: 'Home Renovations', slug: 'home-renovations' },
    { name: 'Electrical Work', slug: 'electrical-work' },
    { name: 'Siding Installation', slug: 'siding-installation' },
    { name: 'Tree Service', slug: 'tree-service' },
    { name: 'Kitchen Remodeling', slug: 'kitchen-remodeling' },
    { name: 'Flooring Repair', slug: 'flooring-repair' },
    { name: 'Custom Carpentry', slug: 'custom-carpentry' },
    { name: 'Trim Carpentry', slug: 'trim-carpentry' }
  ];

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white shadow-lg' : 'bg-white/95 backdrop-blur-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <a href="/" className="flex items-center">
              <img
                src="/logo.svg"
                alt="Horizon Carpentry"
                className="h-8 w-auto"
              />
              <span className="ml-2 text-xl font-bold text-gray-900 hidden md:inline">
                Horizon Carpentry
              </span>
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {/* Locations Dropdown */}
            <div className="relative">
              <button
                onClick={() => setLocationsOpen(!locationsOpen)}
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium"
              >
                Locations
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
              {locationsOpen && (
                <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-50">
                  <div className="grid grid-cols-2 gap-1 p-2">
                    {locations.map((location) => (
                      <a
                        key={location.slug}
                        href={`/${location.slug}`}
                        className="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded"
                        onClick={() => setLocationsOpen(false)}
                      >
                        {location.name}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Services Dropdown */}
            <div className="relative">
              <button
                onClick={() => setServicesOpen(!servicesOpen)}
                className="flex items-center text-gray-700 hover:text-blue-600 font-medium"
              >
                Services
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
              {servicesOpen && (
                <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-50">
                  <div className="grid grid-cols-1 gap-1 p-2">
                    {services.map((service) => (
                      <a
                        key={service.slug}
                        href={currentLocation ? `/${service.slug}/${currentLocation}` : `#services`}
                        className="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded"
                        onClick={() => setServicesOpen(false)}
                      >
                        {service.name}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Regular Nav Items */}
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-blue-600 font-medium"
              >
                {item.name}
              </a>
            ))}
          </div>a
          {/* Phone Button */}
          <div className="flex items-center">
            <a
              href="tel:******-XXX-XXXX"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold flex items-center transition-colors"
            >
              <Phone className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Call Now</span>
              <span className="sm:hidden">(912) XXX-XXXX</span>
            </a>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="md:hidden ml-4 p-2 rounded-md text-gray-700 hover:text-blue-600"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t">
              {/* Mobile Locations */}
              <div className="space-y-1">
                <div className="px-3 py-2 text-sm font-medium text-gray-900">Locations</div>
                <div className="grid grid-cols-2 gap-1 pl-4">
                  {locations.map((location) => (
                    <a
                      key={location.slug}
                      href={`/${location.slug}`}
                      className="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded"
                      onClick={() => setIsOpen(false)}
                    >
                      {location.name}
                    </a>
                  ))}
                </div>
              </div>

              {/* Mobile Services */}
              <div className="space-y-1">
                <div className="px-3 py-2 text-sm font-medium text-gray-900">Services</div>
                <div className="pl-4 space-y-1">
                  {services.slice(0, 6).map((service) => (
                    <a
                      key={service.slug}
                      href={currentLocation ? `/${service.slug}/${currentLocation}` : `#services`}
                      className="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded"
                      onClick={() => setIsOpen(false)}
                    >
                      {service.name}
                    </a>
                  ))}
                </div>
              </div>

              {/* Mobile Nav Items */}
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded"
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;