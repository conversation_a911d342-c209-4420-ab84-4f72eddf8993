---
// Import global styles
import '../index.css';

export interface Props {
  title: string;
  description?: string;
  keywords?: string;
  location?: string;
  service?: string;
}

const {
  title,
  description = "Professional construction and home improvement services in Georgia",
  keywords = "construction, home improvement, deck building, bathroom remodeling",
  location,
  service
} = Astro.props;

// Generate location-specific meta data
const locationTitle = location ? `${title} in ${location}` : title;
const locationDescription = location && service 
  ? `Professional ${service.toLowerCase()} services in ${location}, Georgia. Expert craftsmanship and reliable service.`
  : description;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={locationDescription} />
    <meta name="keywords" content={keywords} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{locationTitle}</title>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={Astro.url} />
    <meta property="og:title" content={locationTitle} />
    <meta property="og:description" content={locationDescription} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={Astro.url} />
    <meta property="twitter:title" content={locationTitle} />
    <meta property="twitter:description" content={locationDescription} />
    
    <!-- Local Business Schema -->
    {location && (
      <script type="application/ld+json" set:html={JSON.stringify({
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Horizon Carpentry",
        "description": locationDescription,
        "address": {
          "@type": "PostalAddress",
          "addressLocality": location,
          "addressRegion": "GA",
          "addressCountry": "US"
        },
        "telephone": "******-XXX-XXXX",
        "url": Astro.url.toString(),
        "serviceArea": location,
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Construction Services",
          "itemListElement": service ? [{
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": service
            }
          }] : []
        }
      })} />
    )}
    
    <!-- Critical CSS for FOUC prevention -->
    <style>
      html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        overflow-x: hidden;
      }
      
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
      }
      
      iframe[src*="leadconnectorhq.com"] * {
        box-sizing: border-box !important;
      }
      
      html:not(.loaded) {
        visibility: hidden;
        opacity: 0;
      }
      
      html.loaded {
        visibility: visible;
        opacity: 1;
        transition: opacity 0.15s ease-in-out;
      }
    </style>
  </head>
  <body>
    <slot />
    
    <script>
      // FOUC prevention
      document.addEventListener('DOMContentLoaded', () => {
        requestAnimationFrame(() => {
          document.documentElement.classList.add('loaded');
        });
      });
    </script>
  </body>
</html>
