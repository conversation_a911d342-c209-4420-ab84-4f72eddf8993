---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import Navigation from '../../components/Navigation.tsx';
import SimpleHeroSection from '../../components/SimpleHeroSection.tsx';
import SimpleBookingSection from '../../components/SimpleBookingSection.tsx';
import SimpleFooter from '../../components/SimpleFooter.tsx';

export async function getStaticPaths() {
  const locations = await getCollection('locations');
  const services = await getCollection('services');
  
  const paths = [];
  for (const service of services) {
    for (const location of locations) {
      paths.push({
        params: { 
          service: service.data.slug, 
          location: location.data.slug 
        },
        props: { 
          service: service.data, 
          location: location.data 
        },
      });
    }
  }
  return paths;
}

const { service, location } = Astro.props;

// Generate service + location specific content
const pageTitle = `${service.name} in ${location.name}, ${location.state}`;
const pageDescription = `Professional ${service.name.toLowerCase()} services in ${location.name}, ${location.state}. ${service.shortDescription}. Licensed contractors serving ${location.county || location.name}.`;
const keywords = `${service.slug.replace('-', ' ')} ${location.name}, ${service.name} ${location.state}, contractors ${location.name}`;
---

<Layout 
  title={pageTitle}
  description={pageDescription}
  keywords={keywords}
  location={location.name}
  service={service.name}
>
  <div class="min-h-screen">
    <Navigation client:load currentLocation={location.slug} />

    <!-- Hero Section -->
    <SimpleHeroSection
      client:load
      location={location.name}
      service={service.name}
      subtitle={service.description}
    />

    <main>

      <!-- Service Features -->
      <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Why Choose Our {service.name} Services in {location.name}?
            </h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {service.features.map((feature) => (
              <div class="bg-white rounded-lg p-6 shadow-md">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{feature}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      <!-- Process Section -->
      {service.process && (
        <section class="py-16">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Our {service.name} Process
              </h2>
              <p class="text-lg text-gray-600">
                Here's how we deliver exceptional {service.name.toLowerCase()} services in {location.name}
              </p>
            </div>
            
            <div class="space-y-8">
              {service.process.map((step, index) => (
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                      {step.step}
                    </div>
                  </div>
                  <div class="ml-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{step.title}</h3>
                    <p class="text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      <!-- Benefits Section -->
      <section class="py-16 bg-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Benefits of Our {service.name} in {location.name}
            </h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {service.benefits.map((benefit) => (
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-lg text-gray-700">{benefit}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <!-- Local Information -->
      <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">
              Serving {location.name} and Surrounding Areas
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">About {location.name}</h3>
                <p class="text-gray-600 mb-4">{location.description}</p>
                {location.population && (
                  <p class="text-sm text-gray-500">Population: {location.population.toLocaleString()}</p>
                )}
              </div>
              <div>
                {location.serviceAreas && (
                  <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Areas We Serve</h3>
                    <ul class="grid grid-cols-1 gap-2">
                      {location.serviceAreas.map((area) => (
                        <li class="flex items-center text-gray-700">
                          <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                          </svg>
                          {area}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      <SimpleBookingSection client:load />
    </main>
    <SimpleFooter client:load />
  </div>
</Layout>
