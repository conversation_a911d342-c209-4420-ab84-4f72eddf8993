---
import { getCollection } from 'astro:content';
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.tsx';
import SimpleHeroSection from '../components/SimpleHeroSection.tsx';
import SimpleBookingSection from '../components/SimpleBookingSection.tsx';
import SimpleFooter from '../components/SimpleFooter.tsx';

const locations = await getCollection('locations');
const services = await getCollection('services');
---

<Layout title="Professional Construction Services in Georgia - Horizon Carpentry">
  <div class="min-h-screen">
    <Navigation client:load />

    <SimpleHeroSection client:load />

    <main>
      
      <!-- Service Areas Section -->
      <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Service Areas
            </h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              We proudly serve communities throughout Southeast Georgia with professional construction and home improvement services.
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {locations.map((location) => (
              <a 
                href={`/${location.data.slug}`}
                class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow group"
              >
                <h3 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600">
                  {location.data.name}
                </h3>
                <p class="text-gray-600 mb-4">
                  {location.data.description}
                </p>
                <div class="flex items-center text-blue-600 font-medium">
                  View Services
                  <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </a>
            ))}
          </div>
        </div>
      </section>
      
      <!-- Services Overview -->
      <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Our Services
            </h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              From deck construction to complete home renovations, we provide comprehensive construction services throughout Georgia.
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                  {service.data.name}
                </h3>
                <p class="text-gray-600 mb-4">
                  {service.data.shortDescription}
                </p>
                <div class="space-y-2">
                  {service.data.features.slice(0, 3).map((feature) => (
                    <div class="flex items-center text-sm text-gray-600">
                      <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      {feature}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      <SimpleBookingSection client:load />
    </main>
    <SimpleFooter client:load />
  </div>
</Layout>
