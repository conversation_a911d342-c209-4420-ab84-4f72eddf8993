---
// Debug page for testing image upload
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width" />
    <meta name="generator" content={Astro.generator} />
    <title>Image Upload Debug</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold mb-8">Image Upload Debug</h1>
      
      <div id="debug-container">
        <!-- Debug component will be loaded here -->
      </div>
    </div>

    <script type="module">
      import ImageUploadDebug from '../components/ImageUploadDebug.tsx';
      import { createRoot } from 'react-dom/client';
      import React from 'react';

      const container = document.getElementById('debug-container');
      if (container) {
        const root = createRoot(container);
        root.render(React.createElement(ImageUploadDebug));
      }
    </script>
  </body>
</html>
