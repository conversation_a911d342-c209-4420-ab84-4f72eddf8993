---
import { getCollection } from 'astro:content';
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.tsx';
import SimpleHeroSection from '../components/SimpleHeroSection.tsx';
import EnhancedBookingSection from '../components/EnhancedBookingSection.tsx';
import SimpleFooter from '../components/SimpleFooter.tsx';

export async function getStaticPaths() {
  const locations = await getCollection('locations');
  return locations.map((location) => ({
    params: { location: location.data.slug },
    props: { location: location.data },
  }));
}

const { location } = Astro.props;
const services = await getCollection('services');

// Generate location-specific content
const pageTitle = `Professional Construction Services in ${location.name}, ${location.state}`;
const pageDescription = `Expert construction, remodeling, and home improvement services in ${location.name}, ${location.state}. Licensed contractors serving ${location.county || location.name} with quality craftsmanship.`;
const keywords = `construction ${location.name}, home improvement ${location.name}, contractors ${location.name}, remodeling ${location.state}`;
---

<Layout 
  title={pageTitle}
  description={pageDescription}
  keywords={keywords}
  location={location.name}
>
  <div class="min-h-screen">
    <Navigation client:load />

    <!-- Location-specific Hero Section -->
    <SimpleHeroSection
      client:load
      location={location.name}
      title={`Professional Construction Services in ${location.name}`}
      subtitle={`Serving ${location.county || location.name} with expert craftsmanship and reliable service`}
    />

    <main>
      
      <!-- Location-specific Services Section -->
      <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
              Our Services in {location.name}
            </h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              We provide comprehensive construction and home improvement services throughout {location.name} and surrounding areas.
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <h3 class="text-xl font-semibold text-gray-900 mb-3">
                  {service.data.name}
                </h3>
                <p class="text-gray-600 mb-4">
                  {service.data.shortDescription}
                </p>
                <a 
                  href={`/${service.data.slug}/${location.slug}`}
                  class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                >
                  Learn More
                  <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      <!-- Location-specific About Section -->
      <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-3xl font-bold text-gray-900 mb-6">
                Why Choose Us in {location.name}?
              </h2>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Local Expertise</h3>
                    <p class="text-gray-600">Deep understanding of {location.name} building codes and local requirements.</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Fast Response</h3>
                    <p class="text-gray-600">Quick response times for all projects in the {location.name} area.</p>
                  </div>
                </div>
                
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Quality Guarantee</h3>
                    <p class="text-gray-600">All work backed by our comprehensive warranty and satisfaction guarantee.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <div class="bg-blue-50 rounded-lg p-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Service Areas in {location.name}
                </h3>
                {location.serviceAreas && (
                  <ul class="space-y-2">
                    {location.serviceAreas.map((area) => (
                      <li class="flex items-center text-gray-700">
                        <svg class="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        {area}
                      </li>
                    ))}
                  </ul>
                )}
                
                {location.zipCodes && (
                  <div class="mt-6">
                    <h4 class="font-medium text-gray-900 mb-2">Zip Codes Served:</h4>
                    <p class="text-gray-600">{location.zipCodes.join(', ')}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <EnhancedBookingSection client:load />
    </main>
    <SimpleFooter client:load />
  </div>
</Layout>
