import { defineCollection, z } from 'astro:content';

const locations = defineCollection({
  type: 'data',
  schema: z.object({
    name: z.string(),
    slug: z.string(),
    state: z.string().default('Georgia'),
    county: z.string().optional(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number(),
    }).optional(),
    population: z.number().optional(),
    description: z.string().optional(),
    serviceAreas: z.array(z.string()).optional(),
    zipCodes: z.array(z.string()).optional(),
  }),
});

const services = defineCollection({
  type: 'data',
  schema: z.object({
    name: z.string(),
    slug: z.string(),
    description: z.string(),
    shortDescription: z.string(),
    category: z.enum(['construction', 'remodeling', 'repair', 'specialty']),
    features: z.array(z.string()),
    benefits: z.array(z.string()),
    process: z.array(z.object({
      step: z.number(),
      title: z.string(),
      description: z.string(),
    })).optional(),
    faqs: z.array(z.object({
      question: z.string(),
      answer: z.string(),
    })).optional(),
    estimatedDuration: z.string().optional(),
    priceRange: z.string().optional(),
    images: z.array(z.string()).optional(),
  }),
});

export const collections = {
  locations,
  services,
};
