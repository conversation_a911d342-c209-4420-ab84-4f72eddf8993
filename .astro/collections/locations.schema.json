{"$ref": "#/definitions/locations", "definitions": {"locations": {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}, "state": {"type": "string", "default": "Georgia"}, "county": {"type": "string"}, "coordinates": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}, "required": ["lat", "lng"], "additionalProperties": false}, "population": {"type": "number"}, "description": {"type": "string"}, "serviceAreas": {"type": "array", "items": {"type": "string"}}, "zipCodes": {"type": "array", "items": {"type": "string"}}, "$schema": {"type": "string"}}, "required": ["name", "slug"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}