{"$ref": "#/definitions/services", "definitions": {"services": {"type": "object", "properties": {"name": {"type": "string"}, "slug": {"type": "string"}, "description": {"type": "string"}, "shortDescription": {"type": "string"}, "category": {"type": "string", "enum": ["construction", "remodeling", "repair", "specialty"]}, "features": {"type": "array", "items": {"type": "string"}}, "benefits": {"type": "array", "items": {"type": "string"}}, "process": {"type": "array", "items": {"type": "object", "properties": {"step": {"type": "number"}, "title": {"type": "string"}, "description": {"type": "string"}}, "required": ["step", "title", "description"], "additionalProperties": false}}, "faqs": {"type": "array", "items": {"type": "object", "properties": {"question": {"type": "string"}, "answer": {"type": "string"}}, "required": ["question", "answer"], "additionalProperties": false}}, "estimatedDuration": {"type": "string"}, "priceRange": {"type": "string"}, "images": {"type": "array", "items": {"type": "string"}}, "$schema": {"type": "string"}}, "required": ["name", "slug", "description", "shortDescription", "category", "features", "benefits"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}